{"$schema": "https://json.schemastore.org/package.json", "name": "opencode", "private": true, "type": "module", "packageManager": "bun@1.2.19", "scripts": {"dev": "bun run --conditions=development packages/opencode/src/index.ts", "typecheck": "bun run --filter='*' typecheck", "generate": "(cd packages/sdk && ./js/script/generate.ts) && (cd packages/sdk/stainless && ./generate.ts)", "postinstall": "./script/hooks"}, "workspaces": {"packages": ["cloud/*", "packages/*", "packages/sdk/js"], "catalog": {"@hono/zod-validator": "0.4.2", "@types/node": "22.13.9", "@tsconfig/node22": "22.0.2", "ai": "5.0.8", "hono": "4.7.10", "typescript": "5.8.2", "zod": "3.25.76", "remeda": "2.26.0", "solid-js": "1.9.9"}}, "dependencies": {"pulumi-stripe": "0.0.24"}, "devDependencies": {"@playwright/test": "1.55.0", "prettier": "3.5.3", "sst": "3.17.12", "ts-node": "10.9.2", "typescript": "5.9.2"}, "repository": {"type": "git", "url": "https://github.com/sst/opencode"}, "license": "MIT", "prettier": {"semi": false, "printWidth": 120}, "trustedDependencies": ["esbuild", "protobufjs", "sharp", "tree-sitter", "tree-sitter-bash", "web-tree-sitter"], "overrides": {"zod": "3.25.76"}, "patchedDependencies": {}}