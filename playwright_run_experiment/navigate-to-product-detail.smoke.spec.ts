import { test, expect } from "@playwright/test"

test("@smoke Navigate to product detail", async ({ page }) => {
  await page.goto("https://www.saucedemo.com/")
  await page.getByPlaceholder("Username").fill("standard_user")
  await page.getByPlaceholder("Password").fill("secret_sauce")
  await page.getByRole("button", { name: "Login" }).click()
  await expect(page).toHaveURL(/.*inventory.html/)
  // Click "Sauce Labs Backpack" product name
  await page.getByText("Sauce Labs Backpack").click()
  // Assert product detail page loads
  await expect(page.locator(".inventory_details_name")).toHaveText("Sauce Labs Backpack")
})

test.afterEach(async ({ page }, testInfo) => {
  if (testInfo.status !== testInfo.expectedStatus) {
    await page.screenshot({ path: `screenshots/@smoke_navigate_to_product_detail.png`, fullPage: true })
  }
})
