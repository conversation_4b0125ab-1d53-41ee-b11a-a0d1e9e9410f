import { test, expect } from "@playwright/test"

const BASE_URL = "https://www.saucedemo.com/"
const USERNAME = "standard_user"
const PASSWORD = "secret_sauce"

test("Add a product to the cart and verify cart icon updates", async ({ page }) => {
  // Go to Login Page
  await page.goto(BASE_URL)

  // Log in
  await page.fill('[data-test="username"]', USERNAME)
  await page.fill('[data-test="password"]', PASSWORD)
  await page.click('[data-test="login-button"]')

  // Ensure we are on the Inventory Page
  await expect(page).toHaveURL(/inventory/)

  // Ensure cart icon is initially empty
  const cartBadge = page.locator(".shopping_cart_badge")
  await expect(cartBadge).toHaveCount(0)

  // Click "Add to cart" for the first product
  const addToCartButton = page.locator('button[data-test^="add-to-cart"]').first()
  await addToCartButton.click()

  // Cart icon should update to reflect "1" item
  await expect(cartBadge).toHaveText("1")
})
