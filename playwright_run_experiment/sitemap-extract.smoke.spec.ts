import { test, expect } from "@playwright/test"

// This test logs in and extracts all visible navigation links and menu items

test("@smoke Extract site map after login", async ({ page }) => {
  await page.goto("https://www.saucedemo.com/")
  await page.getByPlaceholder("Username").fill("standard_user")
  await page.getByPlaceholder("Password").fill("secret_sauce")
  await page.getByRole("button", { name: /login/i }).click()

  // Wait for inventory page
  await expect(page.getByText("Products")).toBeVisible()

  // Extract main menu links (burger menu)
  await page.getByTestId("react-burger-menu-btn").click()
  const menuLinks = await page.locator(".bm-item-list a").allTextContents()

  // Extract product links
  const productLinks = await page.locator(".inventory_item_name").allTextContents()

  // Print site map
  console.log("Site Map:")
  console.log("Main Menu:", menuLinks)
  console.log("Products:", productLinks)
})
