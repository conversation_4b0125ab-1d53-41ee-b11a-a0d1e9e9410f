import { test, expect } from "@playwright/test"

test("@smoke Login button is present and enabled", async ({ page }) => {
  await page.goto("https://www.saucedemo.com/")
  const loginButton = page.getByRole("button", { name: /login/i })
  await expect(loginButton).toBeVisible()
  await expect(loginButton).toBeEnabled()
})

test.afterEach(async ({ page }, testInfo) => {
  if (testInfo.status !== testInfo.expectedStatus) {
    await page.screenshot({ path: `screenshots/${testInfo.title.replace(/\s+/g, "_")}.png`, fullPage: true })
  }
})
