import { test, expect } from "@playwright/test"

test("@smoke Login primary CTA", async ({ page }) => {
  await page.goto("https://www.saucedemo.com/")
  // Fill in demo credentials
  await page.getByPlaceholder("Username").fill("standard_user")
  await page.getByPlaceholder("Password").fill("secret_sauce")
  await page.getByRole("button", { name: /login/i }).click()
  // Assert successful login by checking for inventory page
  await expect(page.getByText("Products")).toBeVisible()
})
