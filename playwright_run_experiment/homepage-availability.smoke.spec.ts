import { test, expect } from "@playwright/test"

test("@smoke Homepage loads and login form is visible", async ({ page }) => {
  await page.goto("https://www.saucedemo.com/")
  // Assert the login form is visible
  await expect(page.getByRole("button", { name: /login/i })).toBeVisible()
  await expect(page.getByPlaceholder("Username")).toBeVisible()
  await expect(page.getByPlaceholder("Password")).toBeVisible()
})

test.afterEach(async ({ page }, testInfo) => {
  if (testInfo.status !== testInfo.expectedStatus) {
    await page.screenshot({ path: `screenshots/${testInfo.title.replace(/\s+/g, "_")}.png`, fullPage: true })
  }
})
