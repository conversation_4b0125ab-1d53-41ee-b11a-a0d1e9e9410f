import { test, expect } from "@playwright/test"

test("@smoke Product navigation", async ({ page }) => {
  await page.goto("https://www.saucedemo.com/")
  // Login first
  await page.getByPlaceholder("Username").fill("standard_user")
  await page.getByPlaceholder("Password").fill("secret_sauce")
  await page.getByRole("button", { name: /login/i }).click()
  // Click first product title
  const firstProduct = page.getByRole("link", { name: /sauce labs backpack/i })
  await firstProduct.click()
  // Assert product detail page loads
  await expect(page.getByText("Sauce Labs Backpack")).toBeVisible()
})
