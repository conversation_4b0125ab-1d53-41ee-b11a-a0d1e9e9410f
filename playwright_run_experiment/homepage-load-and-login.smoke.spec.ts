import { test, expect } from "@playwright/test"

test("@smoke Homepage loads and login works", async ({ page }) => {
  await page.goto("https://www.saucedemo.com/")
  await expect(page.getByPlaceholder("Username")).toBeVisible()
  await expect(page.getByPlaceholder("Password")).toBeVisible()
  await page.getByPlaceholder("Username").fill("standard_user")
  await page.getByPlaceholder("Password").fill("secret_sauce")
  await page.getByRole("button", { name: "Login" }).click()
  await expect(page).toHaveURL(/.*inventory.html/)
})

test.afterEach(async ({ page }, testInfo) => {
  if (testInfo.status !== testInfo.expectedStatus) {
    await page.screenshot({ path: `screenshots/@smoke_homepage_loads_and_login_works.png`, fullPage: true })
  }
})
