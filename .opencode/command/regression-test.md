---
description: Execute a Playwright MCP test from a test case name, including report and video
---

You are responsible for creating, running, and reporting **Playwright MCP tests** based on the test cases in this file: `$ARGUMENTS`.

### 🚨 Mandatory Requirements
Each test execution **must** generate:
- A **Playwright HTML report**
- **Video recordings** of the test run

If **either** is missing, the run is considered **incomplete**.

---

### 🧭 Behavior

#### 1. Input Validation
- If the provided test case (`$ARGUMENTS`) is missing **required inputs** (e.g., a URL or other mandatory parameters),  
  **pause execution and ask the user** to supply the missing values **before proceeding**.
- Only continue once all required inputs are available.

##### 🔹 Standard Prompt Template
When inputs are missing, ask the user in this format:
```
⚠️ Missing Required Input  
The test case **$ARGUMENTS** is missing the following required input(s): <list missing inputs>.  

👉 Please provide the missing value(s) so the test can proceed.
```

#### 2. Generate Test File
- Create a valid Playwright test file using **TypeScript**.
- Name it appropriately based on `$ARGUMENTS`.
- Save the test file in: `./playwright_run_experiment/`

#### 3. Run the Tests
- Use the Playwright **MCP runner** with:
  ```bash
  npx playwright test --config ./mcp_config/playwright.config.ts
  ```
- **DO NOT** use any other flags (e.g., `--reporter`, `--video`, `--trace`) or config overrides.
- **DO NOT** set env variables that affect reporting or artifacts.

#### 4. Collect Outputs
- Ensure the output directory contains:
  - The **HTML report**
  - The **video recording(s)**

> If either one is missing, treat the run as **failed**.

#### 5. Return Results
- Print a summary containing:
  - Path to the HTML report
  - Path(s) to all video recordings generated

---

Execute this workflow based on the input test case: `$ARGUMENTS`.
