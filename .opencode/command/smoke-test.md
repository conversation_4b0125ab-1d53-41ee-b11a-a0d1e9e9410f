---
description: Auto-prioritize and execute Playwright MCP smoke tests from a single URL input, using the standard MCP config, with HTML report and videos
---

You are responsible for **selecting, creating, running, and reporting Playwright MCP smoke tests** from one input: the **base URL** provided as `$ARGUMENTS`.

A **smoke test** must be:
- **Minimal** (fast, 1–3 steps only)  
- **Critical-path focused** (load, primary CTA, navigation, or search)  
- **Blocking** (fail fast, no deep flows)  

---

## 🚨 Mandatory Requirements
Each smoke test execution **must** generate:
- A **Playwright HTML report**  
- **Video recordings** of each test  

If either artifact is missing, treat the run as **failed**.

---

## 🧭 Behavior

### 1) Input Validation (URL only)
- `$ARGUMENTS` must be a valid **base URL**.  
- If missing or invalid, **pause** and ask the user in this format:

```
⚠️ Missing Required Input
The smoke test run needs the following input(s): base URL.

👉 Please provide a valid base URL (e.g., https://staging.example.com) so the smoke tests can proceed.
```

---

### 2) Auto-Prioritize Scenarios
Select up to **3 high-priority smoke flows** based on the base URL:

1. **Availability / Load** → homepage loads, key element visible.  
2. **Primary CTA** → top “Get started” / “Sign in” / “Add to cart” action works.  
3. **Navigation / Search** → nav link or search field leads to a valid page/result.  

Skip anything requiring private creds or payments unless a public demo path is clearly present.

---

### 3) Generate Test Files
- Write Playwright **TypeScript** files in:  
  `./playwright_run_experiment/`  
- Filename: `<slugified-scenario>.smoke.spec.ts`  
- Requirements:
  - Use `@playwright/test`.  
  - Title must include `@smoke`.  
  - Limit to **1–3 critical assertions**.  
  - No `test.slow()`.  
  - Take **screenshot on failure**.  
  - Stable selectors only (role, label, text).  

---

### 4) Run the Tests
Run using the **same MCP config as before**:  
```bash
npx playwright test --config ./mcp_config/playwright.config.ts
```

- **Do not** add extra flags.  
- **Do not** override config or env vars.  

---

### 5) Collect Outputs
- Verify both the **HTML report** and **video recordings** are present.  
- If either is missing → mark run as **failed**.  

---

### 6) Return Results
Provide a summary:
- List of chosen scenarios + 1-line rationale each.  
- Path to the HTML report.  
- Path(s) to all video recordings.  
- Outcome: Passed / Failed.  
- If failed: include failing step + screenshot path.  

---

## ✅ Acceptance Criteria
Run is **successful** only if:
1. All chosen flows complete,  
2. HTML report exists,  
3. Videos exist.  

Else → **failed summary**.  
