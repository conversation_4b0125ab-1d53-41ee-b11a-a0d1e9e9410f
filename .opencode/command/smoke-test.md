---
description: Auto-prioritize and execute Playwright MCP smoke tests from a single URL input, using the standard MCP config, with HTML report and videos. Dynamic site structure is extracted from the base URL via login and DOM inspection.
---

# 🧪 Playwright MCP Smoke Tests – Auth + DOM-Driven Execution

You are responsible for **selecting, creating, running, and reporting Playwright MCP smoke tests**, starting from a **base URL**, and **only after inspecting the site structure through Playwright login and DOM analysis**.

---

## 🚦 Input Validation

- `$ARGUMENTS` must be a valid **base URL**.
- If already provided → proceed.
- If **missing or invalid**, pause and prompt:

```
⚠️ Missing Required Input  
The smoke test run needs the following input(s): base URL.  

👉 Please provide a valid base URL (e.g., https://staging.example.com) so the smoke tests can proceed.
```

---

## 🧱 Site Structure Extraction (SPA / Login-Gated)

1. Load the provided base URL in a **headless Playwright browser**.
2. If page shows no visible structure (JavaScript SPA):
   - Attempt login using demo or provided credentials.
3. After login (e.g., `standard_user / secret_sauce`):
   - Extract visible **navigation links**, **product links**, **menu items**, and **page URLs** via DOM traversal.
   - Use **stable selectors** (`getByRole`, `getByText`, `getByLabel`) only.

### Example: https://www.saucedemo.com

| Route                            | Purpose                     |
|----------------------------------|-----------------------------|
| `/inventory.html`               | Product catalog             |
| `/inventory-item.html?id=X`     | Product details             |
| `/cart.html`                    | Cart                        |
| `/checkout-step-one.html`       | Checkout - step 1           |
| `/checkout-step-two.html`       | Checkout - step 2           |
| `/checkout-complete.html`       | Order confirmation          |
| `/`                             | Login/logout redirect       |
| External: `https://saucelabs.com/` | About page               |

If the structure cannot be extracted due to login failure or JS errors → **halt and report**.

---

## 🧭 Scenario Auto-Prioritization

Based on the extracted DOM structure, choose **up to 3 high-priority smoke flows**:

| Scenario         | Criteria                                                                 |
|------------------|--------------------------------------------------------------------------|
| Homepage Load    | Login page loads and accepts credentials                                 |
| Primary CTA      | Add product to cart and view cart page                                   |
| Nav/Search       | Navigate to product detail or checkout initiation                        |

Avoid:
- Flows requiring payment
- Deep or multi-step form sequences
- Login-required flows without working credentials

---

## 🔒 Use Stable Selectors (Required)

All locators used in the test scripts **must be stable**:

- ✅ Prefer `getByRole`, `getByLabel`, `getByText`  
- ✅ Use `data-testid` or ARIA attributes if available  
- ❌ Avoid using dynamic IDs or brittle CSS paths (`nth-child`, `.class1 > div:nth-child(2)`)  
- ✅ Use semantic HTML and accessibility-first approaches for resilience

If a selector is unstable, auto-replace it during script generation with the most stable fallback.

---

## 🔧 Auto-Heal Locators (Optional but Recommended)

When a test fails due to a missing or incorrect locator (e.g., an ID or text has changed):

1. Automatically re-query the DOM using backup selectors:
   - Use roles (`getByRole`)
   - Use accessible labels (`getByLabel`)
   - Use partial text matches (`getByText`, `locator:has-text()`)

2. If a better match is found (same element with different locator):
   - Update the test script dynamically
   - Log the updated selector
   - Re-run the failed test only

3. If auto-healing fails:
   - Capture error
   - Take a screenshot
   - Mark the scenario as failed and report the broken selector

---

## 🛠 Generate Playwright Test Files

- Language: **TypeScript**
- Location: `./playwright_run_experiment/`
- Filename: `<slugified-scenario>.smoke.spec.ts`
- Conventions:
  - Use `@playwright/test`
  - Title must include `@smoke`
  - Use **1–3 assertions max**
  - Capture **screenshot on failure**
  - Use **stable selectors** only
  - No `test.slow()` allowed

---

## 🧰 Environment Preparation (Auto-Install)

Before executing tests:

1. Check if Playwright is installed. If not, auto-install with:
   ```bash
   npm install -D @playwright/test
   npx playwright install
   ```

2. Ensure the required MCP config exists at:
   ```
   ./mcp_config/playwright.config.ts
   ```
   If missing, halt and notify the user.

3. Automatically install any additional missing dependencies (e.g., `typescript`, `ts-node`) as needed.

---

## ▶️ Execute Tests

Run using the standard MCP configuration:

```bash
npx playwright test --config ./mcp_config/playwright.config.ts
```

- ❌ Do NOT override flags, environment variables, or config paths.

---

## 📦 Required Artifacts

Each run **must generate**:

- ✅ **Playwright HTML report**
- ✅ **Video recordings** for all tests

If either artifact is missing → the run is **considered failed**.

---

## 📋 Final Output Summary

Return a full summary:

- ✅ **Chosen scenarios** + short rationale
- 📄 Path to **HTML report**
- 🎞️ Paths to **videos**
- 🟢 Outcome: `Passed` or `❌ Failed`
- If failed:
  - Show **failing step**
  - Path to **screenshot**

---

## ✅ Acceptance Criteria

Test run is **successful only if**:

1. All chosen flows complete  
2. HTML report is generated  
3. Video recordings are saved  

If any are missing → mark run as **Failed**
