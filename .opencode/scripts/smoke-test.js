#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Get URL from command line arguments
const url = process.argv[2];

if (!url) {
  console.error('⚠️ Missing Required Input');
  console.error('The smoke test run needs the following input(s): base URL.');
  console.error('');
  console.error('👉 Please provide a valid base URL (e.g., https://staging.example.com) so the smoke tests can proceed.');
  process.exit(1);
}

// Validate URL
try {
  new URL(url);
} catch (error) {
  console.error('❌ Invalid URL provided:', url);
  process.exit(1);
}

console.log('🧪 Starting Playwright MCP Smoke Tests');
console.log('🎯 Target URL:', url);

// Ensure test directory exists
const testDir = './playwright_run_experiment';
if (!fs.existsSync(testDir)) {
  fs.mkdirSync(testDir, { recursive: true });
}

// Generate basic smoke test scenarios
const scenarios = [
  {
    name: 'homepage-availability',
    description: 'Homepage loads and key elements are visible'
  },
  {
    name: 'login-primary-cta', 
    description: 'Login functionality works with demo credentials'
  },
  {
    name: 'product-navigation',
    description: 'Product navigation and detail pages load'
  }
];

console.log('📝 Generating test files...');

scenarios.forEach(scenario => {
  const testContent = generateTestFile(scenario, url);
  const filename = `${scenario.name}.smoke.spec.ts`;
  const filepath = path.join(testDir, filename);
  
  fs.writeFileSync(filepath, testContent);
  console.log(`✅ Generated: ${filename}`);
});

// Run the tests
console.log('🚀 Executing tests...');

try {
  const result = execSync('npx playwright test --config ./mcp_config/playwright.config.ts', {
    encoding: 'utf8',
    stdio: 'inherit'
  });
  
  // Check for required artifacts
  const reportPath = './playwright_run_experiment/output/report';
  const artifactsPath = './playwright_run_experiment/output/artifacts';
  
  const hasReport = fs.existsSync(reportPath);
  const hasVideos = fs.existsSync(artifactsPath) && fs.readdirSync(artifactsPath).some(file => file.endsWith('.webm'));
  
  console.log('\n📋 Test Execution Summary:');
  console.log('✅ Chosen scenarios:');
  scenarios.forEach(s => console.log(`   - ${s.name}: ${s.description}`));
  console.log(`📄 HTML Report: ${hasReport ? '✅ Generated' : '❌ Missing'} at ${reportPath}`);
  console.log(`🎞️ Videos: ${hasVideos ? '✅ Generated' : '❌ Missing'} at ${artifactsPath}`);
  
  if (hasReport && hasVideos) {
    console.log('🟢 Outcome: Passed');
  } else {
    console.log('❌ Outcome: Failed - Missing required artifacts');
    process.exit(1);
  }
  
} catch (error) {
  console.error('❌ Test execution failed:', error.message);
  process.exit(1);
}

function generateTestFile(scenario, baseUrl) {
  // This contains the actual test generation logic
  // The sensitive implementation details are hidden here
  switch (scenario.name) {
    case 'homepage-availability':
      return `import { test, expect } from "@playwright/test"

test("@smoke Homepage availability", async ({ page }) => {
  await page.goto("${baseUrl}")
  // Assert login form is visible
  await expect(page.getByRole("button", { name: /login/i })).toBeVisible()
})`;

    case 'login-primary-cta':
      return `import { test, expect } from "@playwright/test"

test("@smoke Login primary CTA", async ({ page }) => {
  await page.goto("${baseUrl}")
  // Fill in demo credentials
  await page.getByPlaceholder("Username").fill("standard_user")
  await page.getByPlaceholder("Password").fill("secret_sauce")
  await page.getByRole("button", { name: /login/i }).click()
  // Assert successful login by checking for inventory page
  await expect(page.getByText("Products")).toBeVisible()
})`;

    case 'product-navigation':
      return `import { test, expect } from "@playwright/test"

test("@smoke Product navigation", async ({ page }) => {
  await page.goto("${baseUrl}")
  // Login first
  await page.getByPlaceholder("Username").fill("standard_user")
  await page.getByPlaceholder("Password").fill("secret_sauce")
  await page.getByRole("button", { name: /login/i }).click()
  // Click first product title
  const firstProduct = page.getByRole("link", { name: /sauce labs backpack/i })
  await firstProduct.click()
  // Assert product detail page loads
  await expect(page.getByText("Sauce Labs Backpack")).toBeVisible()
})`;

    default:
      return `import { test, expect } from "@playwright/test"

test("@smoke ${scenario.name}", async ({ page }) => {
  await page.goto("${baseUrl}")
  await expect(page).toHaveTitle(/.+/)
})`;
  }
}
