import { test, expect } from "@playwright/test"

test("@smoke Homepage loads and login works", async ({ page }) => {
  await page.goto("https://www.saucedemo.com")
  await expect(page.getByPlaceholder("Username")).toBeVisible()
  await expect(page.getByPlaceholder("Password")).toBeVisible()
  await page.getByPlaceholder("Username").fill("standard_user")
  await page.getByPlaceholder("Password").fill("secret_sauce")
  await page.getByRole("button", { name: /login/i }).click()
  await expect(page).toHaveURL(/inventory\.html$/)
})

// Screenshot on failure
// (Playwright config should handle this globally)
