import { test, expect } from "@playwright/test"

test("@smoke Homepage Load & Login", async ({ page }) => {
  await page.goto("https://www.saucedemo.com/")
  await page.locator('input[data-test="username"]').fill("standard_user")
  await page.locator('input[data-test="password"]').fill("secret_sauce")
  await page.locator('input[data-test="login-button"]').click()
  await expect(page).toHaveURL(/.*\/inventory\.html$/)
  await expect(page.getByText("Products")).toBeVisible()
})

test.afterEach(async ({ page }, testInfo) => {
  if (testInfo.status !== testInfo.expectedStatus) {
    await page.screenshot({ path: `screenshots/${testInfo.title.replace(/\s+/g, "_")}.png`, fullPage: true })
  }
})
