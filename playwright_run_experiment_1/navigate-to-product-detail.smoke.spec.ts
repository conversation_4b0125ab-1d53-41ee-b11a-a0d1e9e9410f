import { test, expect } from "@playwright/test"

test("@smoke Navigate to product detail", async ({ page }) => {
  await page.goto("https://www.saucedemo.com")
  await page.getByPlaceholder("Username").fill("standard_user")
  await page.getByPlaceholder("Password").fill("secret_sauce")
  await page.getByRole("button", { name: /login/i }).click()
  await expect(page).toHaveURL(/inventory\.html$/)
  // Click first product title link
  await page.locator('a[id$="_title_link"]').first().click()
  await expect(page).toHaveURL(/inventory-item\.html\?id=\d+$/)
  // Assert Add to Cart button is visible
  await expect(page.getByRole("button", { name: /add to cart/i })).toBeVisible()
})
