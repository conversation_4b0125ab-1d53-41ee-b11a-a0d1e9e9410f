import { test, expect } from "@playwright/test"

test("@smoke Navigate to Product Detail", async ({ page }) => {
  await page.goto("https://www.saucedemo.com/")
  await page.locator('input[data-test="username"]').fill("standard_user")
  await page.locator('input[data-test="password"]').fill("secret_sauce")
  await page.locator('input[data-test="login-button"]').click()
  await page.locator('a[data-test$="title-link"]').first().click()
  await expect(page).toHaveURL(/.*\/inventory-item\.html\?id=\d+$/)
  await expect(page.getByText(/Backpack|Bike Light|T-Shirt|Jacket|Onesie|Test\.allTheThings/)).toBeVisible()
})

test.afterEach(async ({ page }, testInfo) => {
  if (testInfo.status !== testInfo.expectedStatus) {
    await page.screenshot({ path: `screenshots/${testInfo.title.replace(/\s+/g, "_")}.png`, fullPage: true })
  }
})
