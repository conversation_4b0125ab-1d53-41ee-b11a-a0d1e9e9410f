import { test, expect } from "@playwright/test"

test("@smoke Add Product to Cart & View Cart", async ({ page }) => {
  await page.goto("https://www.saucedemo.com/")
  await page.locator('input[data-test="username"]').fill("standard_user")
  await page.locator('input[data-test="password"]').fill("secret_sauce")
  await page.locator('input[data-test="login-button"]').click()
  await page.locator('button[data-test^="add-to-cart-"]').first().click()
  await page.locator('a[data-test="shopping-cart-link"]').click()
  await expect(page).toHaveURL(/.*\/cart\.html$/)
  await expect(page.getByText("Your Cart")).toBeVisible()
})

test.afterEach(async ({ page }, testInfo) => {
  if (testInfo.status !== testInfo.expectedStatus) {
    await page.screenshot({ path: `screenshots/${testInfo.title.replace(/\s+/g, "_")}.png`, fullPage: true })
  }
})
