import { test, expect } from "@playwright/test"

test("@smoke Add product to cart and view cart", async ({ page }) => {
  await page.goto("https://www.saucedemo.com")
  await page.getByPlaceholder("Username").fill("standard_user")
  await page.getByPlaceholder("Password").fill("secret_sauce")
  await page.getByRole("button", { name: /login/i }).click()
  await expect(page).toHaveURL(/inventory\.html$/)
  // Add first product to cart using stable selector
  await page
    .getByRole("button", { name: /add to cart/i })
    .first()
    .click()
  // Go to cart
  await page.locator('[data-test="shopping-cart-link"]').click()
  await expect(page).toHaveURL(/cart\.html$/)
  // Assert product is in cart
  await expect(page.getByRole("button", { name: /checkout/i })).toBeVisible()
})
