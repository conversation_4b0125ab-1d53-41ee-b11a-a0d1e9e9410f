package dialog
import (
	"fmt"
	"strings"

	tea "github.com/charmbracelet/bubbletea/v2"
	"github.com/sst/opencode/internal/app"
	"github.com/sst/opencode/internal/components/list"
	"github.com/sst/opencode/internal/components/modal"
	"github.com/sst/opencode/internal/layout"
	"github.com/sst/opencode/internal/styles"
	"github.com/sst/opencode/internal/theme"
	"github.com/sst/opencode/internal/util"
)

type TestDialog interface {
	layout.Modal
}
type InjectInputValueMsg struct {
	Value string
}

type TestExecutionUseCase struct {
	ID          string
	Title       string
	Description string
}

type testExecutionItem struct {
	UseCase TestExecutionUseCase
}

func (t testExecutionItem) Render(selected bool, width int, baseStyle styles.Style) string {
	theme := theme.CurrentTheme()
	itemStyle := baseStyle.Background(theme.BackgroundPanel()).Foreground(theme.Text())
	if selected {
		itemStyle = itemStyle.Foreground(theme.Primary()).Bold(true)
	}
	title := itemStyle.Render(t.UseCase.Title)
	desc := styles.NewStyle().Foreground(theme.TextMuted()).Render(" " + t.UseCase.Description)
	return strings.Join([]string{title, desc}, "\n")
}

func (t testExecutionItem) Selectable() bool {
	return true
}

type testDialog struct {
	app     *app.App
	modal   *modal.Modal
	list    list.List[testExecutionItem]
	useCases []TestExecutionUseCase
}

func (t *testDialog) Init() tea.Cmd {
	return nil
}

func (t *testDialog) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	switch msg := msg.(type) {
	case tea.WindowSizeMsg:
		t.list.SetMaxWidth(layout.Current.Container.Width - 12)
	case tea.KeyPressMsg:
		if msg.String() == "enter" {
			if item, idx := t.list.GetSelectedItem(); idx >= 0 {
				return t, tea.Sequence(
					util.CmdHandler(modal.CloseModalMsg{}),
					func() tea.Msg {
						switch item.UseCase.ID {
						case "run_from_testcase":
							// TODO: Load file, run tests, output to report folder
							
							return app.SetEditorContentMsg{Text: "/test-exec-from-testcase"}

						case "generate_test_script":
							// TODO: Generate script from test case
							fmt.Println("Generating test script from test case file...")
						case "update_test_script":
							// TODO: Update existing script with new test case
							fmt.Println("Updating test script with new test case...")
						case "convert_format":
							// TODO: Convert Playwright test format to Pytest format
							fmt.Println("Converting test format from Playwright to Pytest...")
						default:
							fmt.Println("Unknown flow selected:", item.UseCase.ID)
						}
						return nil
					},
				)
			}
		}

	}

	var cmd tea.Cmd
	listModel, cmd := t.list.Update(msg)
	t.list = listModel.(list.List[testExecutionItem])
	return t, cmd
}

func (t *testDialog) Render(background string) string {
	content := t.list.View()
	return t.modal.Render(content, background)
}

func (t *testDialog) Close() tea.Cmd {
	return nil
}

func NewTestExecutionDialog(app *app.App) TestDialog {
	useCases := []TestExecutionUseCase{
		{
			ID:          "run_from_testcase",
			Title:       "Execute from Test Case File",
			Description: "Run tests and output a report folder",
		},
		{
			ID:          "generate_test_script",
			Title:       "Generate Test Script",
			Description: "Create a test script folder from case file",
		},
		{
			ID:          "update_test_script",
			Title:       "Update Test Script",
			Description: "Update test script from another test case",
		},
		{
			ID:          "convert_format",
			Title:       "Convert Format",
			Description: "Convert from Playwright to Pytest",
		},
	}

	var items []testExecutionItem
	for _, u := range useCases {
		items = append(items, testExecutionItem{UseCase: u})
	}

	listComponent := list.NewListComponent(
		list.WithItems(items),
		list.WithMaxVisibleHeight[testExecutionItem](10),
		list.WithFallbackMessage[testExecutionItem]("No flows available"),
		list.WithAlphaNumericKeys[testExecutionItem](true),
		list.WithRenderFunc(
			func(item testExecutionItem, selected bool, width int, baseStyle styles.Style) string {
				return item.Render(selected, width, baseStyle)
			},
		),
		list.WithSelectableFunc(func(item testExecutionItem) bool {
			return true
		}),
	)

	listComponent.SetMaxWidth(layout.Current.Container.Width - 12)

	return &testDialog{
		app:     app,
		modal:   modal.New(modal.WithTitle("Select Test Execution Flow"), modal.WithMaxWidth(layout.Current.Container.Width-8)),
		list:    listComponent,
		useCases: useCases,
	}
}
