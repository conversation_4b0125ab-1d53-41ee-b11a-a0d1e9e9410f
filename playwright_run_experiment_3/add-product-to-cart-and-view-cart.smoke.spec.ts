import { test, expect } from "@playwright/test"

test("@smoke add product to cart and view cart", async ({ page }) => {
  await page.goto("https://www.saucedemo.com/")
  await page.locator('[data-test="username"]').fill("standard_user")
  await page.locator('[data-test="password"]').fill("secret_sauce")
  await page.locator('[data-test="login-button"]').click()
  await expect(page).toHaveURL(/inventory\.html/)
  await page.locator('[data-test="add-to-cart-sauce-labs-backpack"]').click()
  await page.locator(".shopping_cart_link").click()
  await expect(page).toHaveURL(/cart\.html/)
  await expect(page.getByText("Sauce Labs Backpack")).toBeVisible()
})
